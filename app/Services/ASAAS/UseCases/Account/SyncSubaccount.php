<?php

namespace App\Services\ASAAS\UseCases\Account;

use App\Services\ASAAS\AccountService;
use App\Services\ASAAS\Factories\AsaasOrganizationFactory;
use App\Services\ASAAS\Repositories\AsaasOrganizationRepository;
use App\Services\ASAAS\UseCases\AsaasLog\LogAsaasRequest;
use App\Services\ASAAS\Exceptions\AsaasException;
use App\Repositories\OrganizationRepository;
use App\Domains\Organization as OrganizationDomain;
use App\Helpers\DBLog;
use Illuminate\Support\Facades\DB;
use Throwable;

class SyncSubaccount
{
    public function __construct(
        private AccountService $accountService,
        private AsaasOrganizationFactory $asaasOrganizationFactory,
        private AsaasOrganizationRepository $asaasOrganizationRepository,
        private OrganizationRepository $organizationRepository,
        private LogAsaasRequest $logAsaasRequest
    ) {}

    /**
     * Sync organization with existing ASAAS subaccount
     *
     * @param OrganizationDomain $organization
     * @param array $searchResults - Result from SearchSubaccounts UseCase
     * @param int $userId
     * @return OrganizationDomain
     * @throws AsaasException
     */
    public function perform(OrganizationDomain $organization, array $searchResults, int $userId): OrganizationDomain
    {
        if ($organization->hasAsaasIntegration()) {
            throw new AsaasException("Organization already has an ASAAS integration");
        }

        if (empty($searchResults['data'])) {
            throw new AsaasException("No subaccounts found to sync with");
        }

        // Get the last account from the array (most recent)
        $lastAccount = end($searchResults['data']);
        $asaasAccountId = $lastAccount->asaas_account_id;

        if (empty($asaasAccountId)) {
            throw new AsaasException("Invalid subaccount data - missing account ID");
        }

        DB::beginTransaction();

        try {
            DBLog::logInfo('Starting ASAAS subaccount sync', 'SyncSubaccount', $organization->id, $userId, [
                'organization_id' => $organization->id,
                'asaas_account_id' => $asaasAccountId,
                'total_found_accounts' => count($searchResults['data'])
            ]);

            // Get the full subaccount details from ASAAS
            $response = $this->accountService->getSubaccount($asaasAccountId);

            $this->logAsaasRequest->perform(
                'GET',
                "/v3/accounts/{$asaasAccountId}",
                [],
                $response,
                $userId,
                $organization->id,
                $this->accountService->getEnvironment()
            );

            // Build AsaasOrganization from the ASAAS response
            $asaasOrganization = $this->asaasOrganizationFactory->buildFromAsaasResponse(
                $response,
                $organization,
                $this->accountService->getEnvironment()
            );

            // Store the AsaasOrganization
            $storedAsaasOrganization = $this->asaasOrganizationRepository->store($asaasOrganization);

            // Set ASAAS integration on the organization
            $organization->setAsaasIntegration($storedAsaasOrganization);

            // Update organization with ASAAS integration data
            $this->organizationRepository->updateAsaasIntegration($organization);

            DB::commit();

            DBLog::logInfo('ASAAS subaccount sync completed successfully', 'SyncSubaccount', 
                $organization->id, $userId, [
                'organization_id' => $organization->id,
                'asaas_account_id' => $asaasAccountId,
                'asaas_organization_id' => $storedAsaasOrganization->id,
                'environment' => $this->accountService->getEnvironment()->value
            ]);

            return $organization;

        } catch (AsaasException $e) {
            DB::rollBack();

            DBLog::logError('Failed to sync ASAAS subaccount - ASAAS error', 'SyncSubaccount', 
                $organization->id, $userId, [
                'organization_id' => $organization->id,
                'asaas_account_id' => $asaasAccountId,
                'error' => $e->getMessage(),
                'asaas_error_code' => $e->getAsaasErrorCode(),
                'response_data' => $e->getResponseData()
            ]);

            throw $e;

        } catch (Throwable $e) {
            DB::rollBack();

            DBLog::logError('Failed to sync ASAAS subaccount - unexpected error', 'SyncSubaccount', 
                $organization->id, $userId, [
                'organization_id' => $organization->id,
                'asaas_account_id' => $asaasAccountId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            throw new AsaasException(
                'Failed to sync subaccount: ' . $e->getMessage(),
                500,
                null,
                []
            );
        }
    }

    /**
     * Check if organization can sync with a subaccount
     *
     * @param OrganizationDomain $organization
     * @return bool
     */
    public function canSyncSubaccount(OrganizationDomain $organization): bool
    {
        // Already has integration
        if ($organization->hasAsaasIntegration()) {
            return false;
        }

        // Check required fields for matching
        if (empty($organization->id) || empty($organization->email)) {
            return false;
        }

        return true;
    }
}
