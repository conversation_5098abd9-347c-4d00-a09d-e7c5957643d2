<?php

namespace App\Services\ASAAS\UseCases\Account;

use App\Domains\Organization as OrganizationDomain;
use App\Services\ASAAS\AccountService;
use App\Services\ASAAS\Domains\Filters\SubaccountFilters;
use App\Services\ASAAS\Factories\AsaasOrganizationFactory;
use App\Services\ASAAS\Exceptions\AsaasException;
use App\Helpers\DBLog;
use Throwable;

class SearchSubaccounts
{
    public function __construct(
        private AccountService $accountService,
        private AsaasOrganizationFactory $asaasOrganizationFactory,
    ) {}

    /**
     * Search subaccounts with filters
     *
     * @param SubaccountFilters $filters
     * @param OrganizationDomain $organization
     * @return array
     * @throws AsaasException
     */
    public function perform(SubaccountFilters $filters, OrganizationDomain $organization): array
    {
        try {
            DBLog::logInfo('Searching ASAAS subaccounts with filters', 'SearchSubaccounts',
                $organization->id, null, [
                'filters' => $filters->toAsaasFilters($organization)
            ]);

            $response = $this->accountService->getSubaccounts($filters->toAsaasFilters($organization));

            $asaasOrganizations = [];
            $accounts = $response['data'] ?? [];

            foreach ($accounts as $accountData) {
                try {
                    $asaasOrganization = $this->asaasOrganizationFactory->buildFromAsaasListResponse($accountData);
                    $asaasOrganizations[] = $asaasOrganization;
                } catch (Throwable $e) {
                    DBLog::logError('Failed to build AsaasOrganization from ASAAS response',
                        'SearchSubaccounts', $organization->id, null, [
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString(),
                        'account_data' => $accountData
                    ]);
                    continue;
                }
            }

            DBLog::logInfo('ASAAS subaccounts search completed successfully',
                'SearchSubaccounts', $organization->id, null, [
                'total_count' => $response['totalCount'] ?? 0,
                'returned_count' => count($asaasOrganizations),
                'has_more' => $response['hasMore'] ?? false
            ]);

            return [
                'data' => $asaasOrganizations,
                'total_count' => $response['totalCount'] ?? 0,
                'has_more' => $response['hasMore'] ?? false,
                'limit' => $response['limit'] ?? 10,
                'offset' => $response['offset'] ?? 0
            ];

        } catch (Throwable $e) {
            DBLog::logError('Failed to search ASAAS subaccounts',
                'SearchSubaccounts', $organization->id, null, [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'filters' => $filters->toAsaasFilters()
            ]);

            if ($e instanceof AsaasException) {
                throw $e;
            }

            throw new AsaasException(
                'Failed to search subaccounts: ' . $e->getMessage(),
                500,
                null
            );
        }
    }
}
