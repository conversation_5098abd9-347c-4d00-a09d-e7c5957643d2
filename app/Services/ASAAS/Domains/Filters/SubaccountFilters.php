<?php

namespace App\Services\ASAAS\Domains\Filters;

use App\Domains\Filters\Filters;

class SubaccountFilters extends Filters
{
    public const ALLOWED_FILTERS = [
        'externalId',
        'email',
        'limit',
        'offset',
    ];

    public function __construct(array $requestData)
    {
        parent::__construct(self::ALLOWED_FILTERS, $requestData);
    }

    /**
     * Get filters formatted for ASAAS API
     */
    public function toAsaasFilters(): array
    {
        return $this->filters;
    }
}
