{"name": "Search Subaccounts", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/asaas/account/subaccounts/search?externalId=1&email=<EMAIL>&limit=20&offset=0", "host": ["{{URL}}"], "path": ["asaas", "account", "subaccounts", "search"], "query": [{"key": "externalId", "value": "1", "description": "Organization ID to search for"}, {"key": "email", "value": "<EMAIL>", "description": "Organization email to search for"}, {"key": "limit", "value": "20", "description": "Number of results to return"}, {"key": "offset", "value": "0", "description": "Number of results to skip"}]}}, "response": []}