<?php

namespace Tests\Feature\Services\ASAAS\UseCases\Account;

use App\Services\ASAAS\UseCases\Account\SearchSubaccounts;
use App\Services\ASAAS\Domains\Filters\SubaccountFilters;
use App\Services\ASAAS\AccountService;
use App\Services\ASAAS\Factories\AsaasOrganizationFactory;
use App\Services\ASAAS\UseCases\AsaasLog\LogAsaasRequest;
use App\Services\ASAAS\Exceptions\AsaasException;
use App\Domains\ASAAS\AsaasOrganization;
use App\Enums\AsaasEnvironment;
use Tests\TestCase;
use Mockery;

class SearchSubaccountsTest extends TestCase
{
    private SearchSubaccounts $useCase;
    private $accountService;
    private $asaasOrganizationFactory;
    private $logAsaasRequest;

    protected function setUp(): void
    {
        parent::setUp();

        $this->accountService = Mockery::mock(AccountService::class);
        $this->asaasOrganizationFactory = Mockery::mock(AsaasOrganizationFactory::class);
        $this->logAsaasRequest = Mockery::mock(LogAsaasRequest::class);

        $this->useCase = new SearchSubaccounts(
            $this->accountService,
            $this->asaasOrganizationFactory,
            $this->logAsaasRequest
        );
    }

    public function test_perform_searches_subaccounts_successfully()
    {
        // Arrange
        $filters = new SubaccountFilters([
            'externalId' => '123',
            'email' => '<EMAIL>',
            'limit' => 20,
            'offset' => 0
        ]);

        $userId = 1;

        $asaasResponse = [
            'data' => [
                [
                    'id' => 'acc_123',
                    'name' => 'Test Organization',
                    'email' => '<EMAIL>',
                    'externalReference' => '123'
                ]
            ],
            'totalCount' => 1,
            'hasMore' => false,
            'limit' => 20,
            'offset' => 0
        ];

        $asaasOrganization = Mockery::mock(AsaasOrganization::class);

        // Mock AccountService
        $this->accountService
            ->shouldReceive('getSubaccounts')
            ->once()
            ->with($filters->toAsaasFilters())
            ->andReturn($asaasResponse);

        $this->accountService
            ->shouldReceive('getEnvironment')
            ->once()
            ->andReturn(AsaasEnvironment::SANDBOX);

        // Mock AsaasOrganizationFactory
        $this->asaasOrganizationFactory
            ->shouldReceive('buildFromAsaasListResponse')
            ->once()
            ->with($asaasResponse['data'][0])
            ->andReturn($asaasOrganization);

        // Mock LogAsaasRequest
        $this->logAsaasRequest
            ->shouldReceive('perform')
            ->once()
            ->with(
                'GET',
                '/v3/accounts',
                $filters->toAsaasFilters(),
                $asaasResponse,
                $userId,
                null,
                AsaasEnvironment::SANDBOX
            );

        // Act
        $result = $this->useCase->perform($filters, $userId);

        // Assert
        $this->assertIsArray($result);
        $this->assertArrayHasKey('data', $result);
        $this->assertArrayHasKey('total_count', $result);
        $this->assertArrayHasKey('has_more', $result);
        $this->assertArrayHasKey('limit', $result);
        $this->assertArrayHasKey('offset', $result);

        $this->assertCount(1, $result['data']);
        $this->assertEquals(1, $result['total_count']);
        $this->assertFalse($result['has_more']);
        $this->assertEquals(20, $result['limit']);
        $this->assertEquals(0, $result['offset']);
        $this->assertSame($asaasOrganization, $result['data'][0]);
    }

    public function test_perform_handles_empty_response()
    {
        // Arrange
        $filters = new SubaccountFilters([
            'externalId' => '999',
            'email' => '<EMAIL>'
        ]);

        $userId = 1;

        $asaasResponse = [
            'data' => [],
            'totalCount' => 0,
            'hasMore' => false,
            'limit' => 10,
            'offset' => 0
        ];

        // Mock AccountService
        $this->accountService
            ->shouldReceive('getSubaccounts')
            ->once()
            ->with($filters->toAsaasFilters())
            ->andReturn($asaasResponse);

        $this->accountService
            ->shouldReceive('getEnvironment')
            ->once()
            ->andReturn(AsaasEnvironment::SANDBOX);

        // Mock LogAsaasRequest
        $this->logAsaasRequest
            ->shouldReceive('perform')
            ->once();

        // Act
        $result = $this->useCase->perform($filters, $userId);

        // Assert
        $this->assertIsArray($result);
        $this->assertEmpty($result['data']);
        $this->assertEquals(0, $result['total_count']);
        $this->assertFalse($result['has_more']);
    }

    public function test_perform_handles_factory_exception_gracefully()
    {
        // Arrange
        $filters = new SubaccountFilters([
            'externalId' => '123'
        ]);

        $userId = 1;

        $asaasResponse = [
            'data' => [
                [
                    'id' => 'acc_123',
                    'name' => 'Test Organization'
                ],
                [
                    'id' => 'acc_456',
                    'name' => 'Another Organization'
                ]
            ],
            'totalCount' => 2,
            'hasMore' => false,
            'limit' => 10,
            'offset' => 0
        ];

        $asaasOrganization = Mockery::mock(AsaasOrganization::class);

        // Mock AccountService
        $this->accountService
            ->shouldReceive('getSubaccounts')
            ->once()
            ->andReturn($asaasResponse);

        $this->accountService
            ->shouldReceive('getEnvironment')
            ->once()
            ->andReturn(AsaasEnvironment::SANDBOX);

        // Mock AsaasOrganizationFactory - first call succeeds, second throws exception
        $this->asaasOrganizationFactory
            ->shouldReceive('buildFromAsaasListResponse')
            ->with($asaasResponse['data'][0])
            ->once()
            ->andReturn($asaasOrganization);

        $this->asaasOrganizationFactory
            ->shouldReceive('buildFromAsaasListResponse')
            ->with($asaasResponse['data'][1])
            ->once()
            ->andThrow(new \Exception('Factory error'));

        // Mock LogAsaasRequest
        $this->logAsaasRequest
            ->shouldReceive('perform')
            ->once();

        // Act
        $result = $this->useCase->perform($filters, $userId);

        // Assert - Should only return the successfully built organization
        $this->assertCount(1, $result['data']);
        $this->assertSame($asaasOrganization, $result['data'][0]);
        $this->assertEquals(2, $result['total_count']); // Original total count preserved
    }

    public function test_perform_throws_asaas_exception_on_service_error()
    {
        // Arrange
        $filters = new SubaccountFilters([
            'externalId' => '123'
        ]);

        $userId = 1;

        $asaasException = new AsaasException('ASAAS API Error', 400, 'INVALID_REQUEST', []);

        // Mock AccountService to throw AsaasException
        $this->accountService
            ->shouldReceive('getSubaccounts')
            ->once()
            ->andThrow($asaasException);

        // Act & Assert
        $this->expectException(AsaasException::class);
        $this->expectExceptionMessage('ASAAS API Error');

        $this->useCase->perform($filters, $userId);
    }

    public function test_perform_wraps_generic_exception_in_asaas_exception()
    {
        // Arrange
        $filters = new SubaccountFilters([
            'externalId' => '123'
        ]);

        $userId = 1;

        // Mock AccountService to throw generic exception
        $this->accountService
            ->shouldReceive('getSubaccounts')
            ->once()
            ->andThrow(new \Exception('Generic error'));

        // Act & Assert
        $this->expectException(AsaasException::class);
        $this->expectExceptionMessage('Failed to search subaccounts: Generic error');
        $this->expectExceptionCode(500);

        $this->useCase->perform($filters, $userId);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}
